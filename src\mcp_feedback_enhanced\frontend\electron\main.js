import { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog, shell } from 'electron'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { createRequire } from 'module'

const require = createRequire(import.meta.url)
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 開發模式檢測
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged || !app.isPackaged

// 主窗口引用
let mainWindow

// 解析命令行參數
function parseCommandLineArgs() {
  const args = process.argv.slice(2)
  const config = {
    sessionId: null,
    serverHost: '127.0.0.1',
    serverPort: 8000,
    mode: 'local' // 'local' 或 'remote'
  }

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    if (arg === '--session-id' && i + 1 < args.length) {
      config.sessionId = args[i + 1]
      i++
    } else if (arg === '--server-host' && i + 1 < args.length) {
      config.serverHost = args[i + 1]
      i++
    } else if (arg === '--server-port' && i + 1 < args.length) {
      config.serverPort = parseInt(args[i + 1])
      i++
    } else if (arg === '--mode' && i + 1 < args.length) {
      config.mode = args[i + 1]
      i++
    }
  }

  console.log('Electron 啟動配置:', config)
  return config
}

const launchConfig = parseCommandLineArgs()

function createWindow() {
  // 創建瀏覽器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, 'preload.js')
    },
    icon: join(__dirname, '../public/icon.png'),
    show: false,
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
  })

  // 載入應用
  if (isDev) {
    // 等待 Vite 服務器啟動（優化：減少等待時間）
    setTimeout(async () => {
      try {
        await mainWindow.loadURL('http://localhost:5173')
        console.log('Successfully loaded Vite dev server')
        // 移除自動打開開發者工具以加快啟動速度
        // mainWindow.webContents.openDevTools()
      } catch (error) {
        console.error('Failed to load Vite dev server:', error)
        // 如果無法載入開發服務器，顯示錯誤頁面
        mainWindow.loadURL('data:text/html,<h1>Loading...</h1><p>Please make sure Vite dev server is running on http://localhost:5173</p>')
      }
    }, 1000)
  } else {
    mainWindow.loadFile(join(__dirname, '../dist/index.html'))
  }

  // 窗口準備好後顯示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // 聚焦窗口
    if (isDev) {
      mainWindow.focus()
    }

    // 將啟動配置發送給渲染進程（優化：減少延遲）
    setTimeout(() => {
      mainWindow.webContents.send('launch-config', launchConfig)
    }, 300)
  })

  // 處理窗口關閉
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // 處理外部連結
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// 應用準備就緒
app.whenReady().then(() => {
  createWindow()
  
  // 設置應用菜單
  createMenu()
  
  // macOS 特殊處理
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

// 所有窗口關閉時退出應用 (macOS 除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 創建應用菜單
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Session',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-session')
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click: () => {
            mainWindow.webContents.send('menu-about')
          }
        }
      ]
    }
  ]

  // macOS 菜單調整
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    })
  }

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// IPC 處理程序
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options)
  return result
})

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options)
  return result
})

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options)
  return result
})

// 錯誤處理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})
