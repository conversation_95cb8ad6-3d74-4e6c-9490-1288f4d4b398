{"app": {"title": "MCP <PERSON>ced", "description": "AI 辅助开发交互式反馈收集工具"}, "navigation": {"feedback": "反馈", "summary": "摘要", "command": "命令", "settings": "设置", "about": "关于"}, "feedback": {"title": "反馈收集", "placeholder": "请输入您的反馈内容...", "imageUpload": "图片上传", "dragDropHint": "拖拽图片到此处或点击上传", "pasteHint": "您也可以使用 Ctrl+V 粘贴剪贴板中的图片", "maxImages": "最多可上传 {count} 张图片", "imageSize": "图片大小限制：{size}KB", "removeImage": "移除图片", "clearAll": "清除全部", "submit": "提交反馈", "submitting": "提交中...", "success": "反馈提交成功", "error": "反馈提交失败"}, "summary": {"title": "AI 工作摘要", "projectDir": "项目目录", "content": "摘要内容", "empty": "暂无摘要内容"}, "command": {"title": "命令执行", "input": "输入命令", "placeholder": "请输入要执行的命令...", "execute": "执行", "executing": "执行中...", "clear": "清除日志", "logs": "执行日志", "noLogs": "暂无执行日志", "timeout": "命令执行超时", "error": "命令执行错误"}, "settings": {"title": "设置", "language": "语言设置", "theme": "主题设置", "layout": "布局设置", "server": "服务器设置", "shortcuts": "快捷键设置", "advanced": "高级设置", "reset": "重置为默认值", "save": "保存设置", "saved": "设置已保存"}, "layout": {"separate": "分离模式", "combinedVertical": "合并模式（垂直）", "combinedHorizontal": "合并模式（水平）"}, "timeout": {"enabled": "启用超时", "duration": "超时时间", "remaining": "剩余时间", "expired": "已超时", "warning": "即将超时"}, "websocket": {"connecting": "连接中...", "connected": "已连接", "disconnected": "已断线", "error": "连接错误", "reconnecting": "重新连接中..."}, "about": {"title": "关于", "version": "版本", "author": "作者", "description": "描述", "license": "许可", "repository": "代码库", "documentation": "说明文档", "support": "技术支持"}, "common": {"ok": "确定", "cancel": "取消", "yes": "是", "no": "否", "save": "保存", "delete": "删除", "edit": "编辑", "close": "关闭", "loading": "加载中...", "error": "错误", "success": "成功", "warning": "警告", "info": "信息"}, "errors": {"networkError": "网络连接错误", "serverError": "服务器错误", "invalidInput": "输入无效", "fileTooBig": "文件过大", "unsupportedFormat": "不支持的格式", "uploadFailed": "上传失败", "commandFailed": "命令执行失败", "timeoutError": "操作超时", "unknownError": "未知错误"}, "shortcuts": {"submit": "提交", "clear": "清除", "newSession": "新建会话", "toggleLayout": "切换布局", "focusInput": "聚焦输入框"}}