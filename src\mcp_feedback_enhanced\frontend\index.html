<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MCP Feedback Enhanced</title>
    <meta name="description" content="Enhanced MCP server for interactive user feedback and command execution in AI-assisted development" />
    <style>
      /* 載入動畫 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #1a1a1a;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: #409eff;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #333;
        border-top: 3px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 16px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 基礎樣式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background: #1a1a1a;
        color: #e5e5e5;
        overflow: hidden;
      }
      
      #app {
        height: 100vh;
        width: 100vw;
      }
    </style>
  </head>
  <body>
    <div id="loading">
      <div class="loading-spinner"></div>
      <span>載入中...</span>
    </div>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
