<template>
  <div id="app" class="app-container">
    <!-- 使用全局佈局 -->
    <AppLayout>
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </AppLayout>

    <!-- 全局通知容器 -->
    <div id="notification-container"></div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { useSettingsStore } from '@/stores/settings'
import { useWebSocketStore } from '@/stores/websocket'
import AppLayout from '@/components/AppLayout.vue'

const settingsStore = useSettingsStore()
const wsStore = useWebSocketStore()

// 組件掛載時初始化
onMounted(async () => {
  // 載入設定
  await settingsStore.loadSettings()
  
  // 應用主題
  applyTheme()
  
  // 初始化 WebSocket 連接（如果需要）
  if (settingsStore.autoConnect) {
    wsStore.connect()
  }
  
  // 監聽 Electron 菜單事件
  if (window.electronAPI) {
    setupElectronMenuListeners()
  }
})

// 組件卸載時清理
onUnmounted(() => {
  wsStore.disconnect()
})

// 應用主題
function applyTheme() {
  const html = document.documentElement
  
  // 設置深色主題
  html.classList.add('dark')
  
  // 設置主題色
  html.style.setProperty('--el-color-primary', settingsStore.themeColor)
}

// 設置 Electron 菜單監聽器
function setupElectronMenuListeners() {
  // 新建會話
  window.electronAPI.onMenuNewSession(() => {
    // 觸發新建會話邏輯
    console.log('New session requested from menu')
  })
  
  // 關於對話框
  window.electronAPI.onMenuAbout(() => {
    // 顯示關於對話框
    console.log('About dialog requested from menu')
  })
}
</script>

<style>
/* 全局樣式 */
.app-container {
  height: 100vh;
  width: 100vw;
  background: var(--el-bg-color);
  color: var(--el-text-color-primary);
  overflow: hidden;
}

/* 路由過渡動畫 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}

/* Element Plus 深色主題自定義 */
.dark {
  --el-bg-color: #1a1a1a;
  --el-bg-color-page: #0d1117;
  --el-bg-color-overlay: #161b22;
  --el-text-color-primary: #e6edf3;
  --el-text-color-regular: #c9d1d9;
  --el-text-color-secondary: #8b949e;
  --el-text-color-placeholder: #6e7681;
  --el-border-color: #30363d;
  --el-border-color-light: #21262d;
  --el-border-color-lighter: #161b22;
  --el-fill-color: #21262d;
  --el-fill-color-light: #30363d;
  --el-fill-color-lighter: #262c36;
  --el-fill-color-extra-light: #1c2128;
  --el-fill-color-dark: #161b22;
  --el-fill-color-darker: #0d1117;
  --el-fill-color-blank: transparent;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .app-container {
    font-size: 14px;
  }
}

/* 無障礙支援 */
@media (prefers-reduced-motion: reduce) {
  .fade-enter-active,
  .fade-leave-active {
    transition: none;
  }
}

/* 高對比度模式 */
@media (prefers-contrast: high) {
  .dark {
    --el-border-color: #ffffff;
    --el-text-color-primary: #ffffff;
  }
}
</style>
