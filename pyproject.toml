[project]
name = "mcp-feedback-enhanced"
version = "2.2.3"
description = "Enhanced MCP server for interactive user feedback and command execution in AI-assisted development, featuring dual UI support (Qt GUI and Web UI) with intelligent environment detection."
readme = "README.md"
requires-python = ">=3.11"
authors = [
    { name = "Minidoracat", email = "<EMAIL>" }
]
keywords = ["mcp", "ai", "feedback", "gui", "web-ui", "interactive", "development"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: User Interfaces",
]
dependencies = [
    "fastmcp>=2.0.0",
    "psutil>=7.0.0",
    "pyside6>=*******",
    "fastapi>=0.115.0",
    "uvicorn>=0.30.0",
    "websockets>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
]

[project.urls]
Homepage = "https://github.com/Minidoracat/mcp-feedback-enhanced"
Repository = "https://github.com/Minidoracat/mcp-feedback-enhanced"
Issues = "https://github.com/Minidoracat/mcp-feedback-enhanced/issues"

[project.scripts]
mcp-feedback-enhanced = "mcp_feedback_enhanced.__main__:main"
interactive-feedback-mcp = "mcp_feedback_enhanced.__main__:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_feedback_enhanced"]

[tool.uv]
dev-dependencies = [
    "bump2version>=1.0.1",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "twine>=6.1.0",
]
