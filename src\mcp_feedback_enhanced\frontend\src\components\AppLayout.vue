<template>
  <div class="app-layout">
    <!-- 固定側邊欄 -->
    <div class="sidebar-container" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="app-logo" v-if="!sidebarCollapsed">
          <el-icon size="24"><ChatDotRound /></el-icon>
          <span class="app-title">MCP Feedback</span>
        </div>
        <el-button 
          class="collapse-btn"
          :icon="sidebarCollapsed ? Expand : Fold"
          circle
          size="small"
          @click="toggleSidebar"
        />
      </div>
      
      <el-menu
        :default-active="$route.name"
        router
        :collapse="sidebarCollapsed"
        background-color="var(--el-bg-color-overlay)"
        text-color="var(--el-text-color-primary)"
        active-text-color="var(--el-color-primary)"
        class="sidebar-menu"
      >
        <el-menu-item index="feedback">
          <el-icon><ChatDotRound /></el-icon>
          <template #title>{{ $t('navigation.feedback') }}</template>
        </el-menu-item>
        <el-menu-item index="summary">
          <el-icon><Document /></el-icon>
          <template #title>{{ $t('navigation.summary') }}</template>
        </el-menu-item>
        <el-menu-item index="command">
          <el-icon><Monitor /></el-icon>
          <template #title>{{ $t('navigation.command') }}</template>
        </el-menu-item>
        <el-menu-item index="settings">
          <el-icon><Setting /></el-icon>
          <template #title>{{ $t('navigation.settings') }}</template>
        </el-menu-item>
        <el-menu-item index="about">
          <el-icon><InfoFilled /></el-icon>
          <template #title>{{ $t('navigation.about') }}</template>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 主要內容區域 -->
    <div class="main-container" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  ChatDotRound, Document, Monitor, Setting, InfoFilled,
  Expand, Fold
} from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'

const settingsStore = useSettingsStore()

// 側邊欄狀態
const sidebarCollapsed = ref(false)

// 切換側邊欄
function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存到設定中
  settingsStore.sidebarCollapsed = sidebarCollapsed.value
  settingsStore.saveSettings()
}

onMounted(() => {
  // 載入側邊欄狀態
  sidebarCollapsed.value = settingsStore.sidebarCollapsed || false
})
</script>

<style scoped>
.app-layout {
  height: 100vh;
  background: var(--el-bg-color);
  display: flex;
}

/* 固定側邊欄 */
.sidebar-container {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 240px;
  background: var(--el-bg-color-overlay);
  border-right: 1px solid var(--el-border-color);
  transition: width 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.sidebar-container.collapsed {
  width: 64px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 12px;
  border-bottom: 1px solid var(--el-border-color);
  min-height: 64px;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-color-primary);
  font-weight: bold;
  font-size: 16px;
}

.app-title {
  white-space: nowrap;
}

.collapse-btn {
  flex-shrink: 0;
}

.sidebar-menu {
  flex: 1;
  border: none;
  overflow-y: auto;
}

/* 主要內容區域 */
.main-container {
  flex: 1;
  margin-left: 240px;
  transition: margin-left 0.3s ease;
  height: 100vh;
  overflow-y: auto;
}

.main-container.sidebar-collapsed {
  margin-left: 64px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .sidebar-container {
    width: 200px;
  }
  
  .sidebar-container.collapsed {
    width: 64px;
  }
  
  .main-container {
    margin-left: 200px;
  }
  
  .main-container.sidebar-collapsed {
    margin-left: 64px;
  }
}

/* 菜單項目樣式優化 */
:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-menu-item:hover) {
  background-color: var(--el-fill-color-light) !important;
}

:deep(.el-menu-item.is-active) {
  background-color: var(--el-color-primary-light-9) !important;
  color: var(--el-color-primary) !important;
}

:deep(.el-menu-item.is-active .el-icon) {
  color: var(--el-color-primary) !important;
}

/* 收縮狀態下的樣式 */
:deep(.el-menu--collapse .el-menu-item) {
  margin: 4px;
  text-align: center;
}
</style>
