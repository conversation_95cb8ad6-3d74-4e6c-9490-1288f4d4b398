# MCP Feedback Enhanced - Frontend

基於 Electron + Vue 3 的統一前端界面

## 🚀 快速開始

### 安裝依賴
```bash
cd frontend
npm install
```

### 開發模式
```bash
# Web 開發模式
npm run dev

# Electron 開發模式
npm run electron:dev
```

### 構建
```bash
# 構建 Web 版本
npm run build

# 構建 Electron 應用
npm run electron:build
```

## 📁 項目結構

```
frontend/
├── electron/           # Electron 主進程
│   ├── main.js        # 主進程入口
│   └── preload.js     # 預載腳本
├── src/
│   ├── views/         # 頁面視圖
│   ├── components/    # Vue 組件 (待創建)
│   ├── stores/        # Pinia 狀態管理
│   ├── i18n/          # 國際化
│   ├── router/        # 路由配置
│   └── utils/         # 工具函數 (待創建)
├── public/            # 靜態資源
└── package.json       # 依賴配置
```

## 🎯 功能特性

- ✅ Electron + Vue 3 + Vite 架構
- ✅ Element Plus UI 組件庫
- ✅ Pinia 狀態管理
- ✅ Vue I18n 國際化 (繁中/簡中/英文)
- ✅ 深色主題設計
- ✅ 響應式佈局
- ✅ 基礎頁面視圖

## 🔧 技術棧

- **框架**: Vue 3 + Composition API
- **構建工具**: Vite
- **桌面框架**: Electron
- **UI 庫**: Element Plus
- **狀態管理**: Pinia
- **國際化**: Vue I18n
- **開發語言**: JavaScript

## 📝 開發說明

### 當前狀態
- ✅ 基礎架構搭建完成
- ✅ 路由和狀態管理配置完成
- ✅ 基本頁面視圖創建完成
- 🔄 可以啟動測試查看界面

### 下一步計劃
1. 創建可復用組件
2. 實現與後端 API 的通信
3. 完善圖片上傳功能
4. 實現命令執行功能
5. 添加更多交互特性

## 🚀 測試運行

現在可以運行以下命令來測試界面：

```bash
cd frontend
npm install
npm run dev
```

然後在瀏覽器中訪問 http://localhost:5173 查看界面。

要測試 Electron 版本：
```bash
npm run electron:dev
```
