<template>
  <div class="summary-view">
    <h2>{{ $t('summary.title') }}</h2>
    
    <el-card class="summary-card">
      <div v-if="feedbackStore.aiSummary" class="summary-content">
        <div class="summary-text">
          {{ feedbackStore.aiSummary }}
        </div>
      </div>
      <el-empty v-else :description="$t('summary.empty')" />
    </el-card>
  </div>
</template>

<script setup>
import { useFeedbackStore } from '@/stores/feedback'

const feedbackStore = useFeedbackStore()
</script>

<style scoped>
.summary-view {
  padding: 20px;
  height: 100%;
}

.summary-card {
  margin-top: 20px;
}

.summary-content {
  line-height: 1.6;
}

.summary-text {
  white-space: pre-wrap;
  color: var(--el-text-color-primary);
}
</style>
