import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'
import { setupI18n } from './i18n'

// 創建應用實例
const app = createApp(App)

// 創建 Pinia 狀態管理
const pinia = createPinia()

// 設置國際化
const i18n = setupI18n()

// 使用插件
app.use(pinia)
app.use(router)
app.use(i18n)
app.use(ElementPlus)

// 全局屬性
app.config.globalProperties.$isElectron = window.environment?.isElectron || false

// 錯誤處理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
}

// 掛載應用
app.mount('#app')

// 隱藏載入動畫
const loadingElement = document.getElementById('loading')
if (loadingElement) {
  loadingElement.style.display = 'none'
}

// 監聽 Electron 啟動配置
if (window.environment?.isElectron && window.electronAPI?.onLaunchConfig) {
  window.electronAPI.onLaunchConfig((config) => {
    console.log('收到 Electron 啟動配置:', config)

    // 獲取設置 store 並更新會話信息
    import('./stores/settings').then(({ useSettingsStore }) => {
      const settingsStore = useSettingsStore()
      settingsStore.setSessionInfo(config)

      // 如果有會話 ID，自動連接 WebSocket（優化：減少延遲）
      if (config.sessionId) {
        import('./stores/websocket').then(({ useWebSocketStore }) => {
          const wsStore = useWebSocketStore()
          setTimeout(() => {
            wsStore.connect()
          }, 300)
        })
      }
    })
  })
}

// 開發模式下的調試信息
if (import.meta.env.DEV) {
  console.log('🚀 MCP Feedback Enhanced Frontend Started')
  console.log('Environment:', {
    isElectron: window.environment?.isElectron,
    platform: window.environment?.platform,
    versions: window.environment?.versions
  })
}
