import { createI18n } from 'vue-i18n'
import zhTW from './locales/zh-TW.json'
import zhCN from './locales/zh-CN.json'
import en from './locales/en.json'

// 支援的語言列表
export const supportedLocales = [
  { code: 'zh-TW', name: '繁體中文', flag: '🇹🇼' },
  { code: 'zh-CN', name: '简体中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' }
]

// 檢測系統語言
function detectSystemLocale() {
  const systemLang = navigator.language || navigator.userLanguage
  
  // 語言映射
  const langMap = {
    'zh-TW': 'zh-TW',
    'zh-HK': 'zh-TW',
    'zh-MO': 'zh-TW',
    'zh-CN': 'zh-CN',
    'zh-SG': 'zh-CN',
    'zh': 'zh-CN',
    'en': 'en',
    'en-US': 'en',
    'en-GB': 'en'
  }
  
  return langMap[systemLang] || 'zh-TW'
}

// 創建 i18n 實例
export function setupI18n() {
  const savedLocale = localStorage.getItem('mcp-feedback-locale')
  const defaultLocale = savedLocale || detectSystemLocale()
  
  const i18n = createI18n({
    legacy: false,
    locale: defaultLocale,
    fallbackLocale: 'zh-TW',
    messages: {
      'zh-TW': zhTW,
      'zh-CN': zhCN,
      'en': en
    },
    globalInjection: true,
    silentTranslationWarn: true,
    silentFallbackWarn: true
  })
  
  return i18n
}

// 切換語言
export function setLocale(i18n, locale) {
  if (supportedLocales.some(l => l.code === locale)) {
    i18n.global.locale.value = locale
    localStorage.setItem('mcp-feedback-locale', locale)
    document.documentElement.lang = locale
  }
}

// 獲取當前語言
export function getCurrentLocale(i18n) {
  return i18n.global.locale.value
}

// 獲取語言顯示名稱
export function getLocaleDisplayName(locale) {
  const found = supportedLocales.find(l => l.code === locale)
  return found ? found.name : locale
}
