import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useSettingsStore } from './settings'

export const useWebSocketStore = defineStore('websocket', () => {
  // 狀態
  const socket = ref(null)
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const lastError = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(5)
  const reconnectDelay = ref(1000) // 毫秒
  const reconnectTimer = ref(null)
  
  // 消息隊列
  const messageQueue = ref([])
  const receivedMessages = ref([])
  
  // 計算屬性
  const connectionStatus = computed(() => {
    if (isConnecting.value) return 'connecting'
    if (isConnected.value) return 'connected'
    if (lastError.value) return 'error'
    return 'disconnected'
  })
  
  const canReconnect = computed(() => {
    return !isConnected.value && 
           !isConnecting.value && 
           reconnectAttempts.value < maxReconnectAttempts.value
  })
  
  // 動作
  function connect() {
    if (isConnected.value || isConnecting.value) {
      return
    }
    
    const settingsStore = useSettingsStore()
    const wsUrl = settingsStore.wsUrl
    
    isConnecting.value = true
    lastError.value = null
    
    try {
      socket.value = new WebSocket(wsUrl)
      
      socket.value.onopen = handleOpen
      socket.value.onmessage = handleMessage
      socket.value.onclose = handleClose
      socket.value.onerror = handleError
      
      console.log(`Connecting to WebSocket: ${wsUrl}`)
    } catch (error) {
      handleError(error)
    }
  }
  
  function disconnect() {
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
    
    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
    
    isConnected.value = false
    isConnecting.value = false
    reconnectAttempts.value = 0
  }
  
  function send(message) {
    if (!isConnected.value) {
      // 如果未連接，將消息加入隊列
      messageQueue.value.push(message)
      
      // 嘗試重新連接
      if (canReconnect.value) {
        connect()
      }
      return false
    }
    
    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
      socket.value.send(messageStr)
      return true
    } catch (error) {
      console.error('Failed to send message:', error)
      lastError.value = error
      return false
    }
  }
  
  function sendCommand(command, data = {}) {
    return send({
      type: 'command',
      command,
      data,
      timestamp: new Date().toISOString()
    })
  }
  
  function sendFeedback(feedbackData) {
    return send({
      type: 'feedback',
      data: feedbackData,
      timestamp: new Date().toISOString()
    })
  }
  
  function handleOpen(event) {
    console.log('WebSocket connected')
    isConnected.value = true
    isConnecting.value = false
    reconnectAttempts.value = 0
    lastError.value = null
    
    // 發送隊列中的消息
    while (messageQueue.value.length > 0) {
      const message = messageQueue.value.shift()
      send(message)
    }
  }
  
  function handleMessage(event) {
    try {
      const message = JSON.parse(event.data)
      receivedMessages.value.push({
        ...message,
        receivedAt: new Date().toISOString()
      })
      
      // 處理特定類型的消息
      handleSpecificMessage(message)
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }
  
  function handleClose(event) {
    console.log('WebSocket disconnected:', event.code, event.reason)
    isConnected.value = false
    isConnecting.value = false
    socket.value = null
    
    // 如果不是主動關閉，嘗試重新連接
    if (event.code !== 1000 && canReconnect.value) {
      scheduleReconnect()
    }
  }
  
  function handleError(error) {
    console.error('WebSocket error:', error)
    lastError.value = error
    isConnecting.value = false
    
    if (canReconnect.value) {
      scheduleReconnect()
    }
  }
  
  function scheduleReconnect() {
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
    }
    
    const delay = reconnectDelay.value * Math.pow(2, reconnectAttempts.value)
    
    reconnectTimer.value = setTimeout(() => {
      reconnectAttempts.value++
      console.log(`Attempting to reconnect (${reconnectAttempts.value}/${maxReconnectAttempts.value})`)
      connect()
    }, delay)
  }
  
  function handleSpecificMessage(message) {
    switch (message.type) {
      case 'session_created':
        console.log('Session created:', message.data)
        break
      case 'command_result':
        console.log('Command result:', message.data)
        break
      case 'error':
        console.error('Server error:', message.data)
        break
      case 'ping':
        // 回應 pong
        send({ type: 'pong', timestamp: new Date().toISOString() })
        break
      default:
        console.log('Unknown message type:', message.type)
    }
  }
  
  function clearMessages() {
    receivedMessages.value = []
  }
  
  function clearQueue() {
    messageQueue.value = []
  }
  
  function getRecentMessages(limit = 50) {
    return receivedMessages.value.slice(-limit)
  }
  
  return {
    // 狀態
    isConnected,
    isConnecting,
    lastError,
    reconnectAttempts,
    messageQueue,
    receivedMessages,
    
    // 計算屬性
    connectionStatus,
    canReconnect,
    
    // 動作
    connect,
    disconnect,
    send,
    sendCommand,
    sendFeedback,
    clearMessages,
    clearQueue,
    getRecentMessages
  }
})
