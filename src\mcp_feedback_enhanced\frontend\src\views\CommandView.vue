<template>
  <div class="command-view">
    <h2>{{ $t('command.title') }}</h2>
    
    <el-card class="command-input-card">
      <div class="command-input-area">
        <el-input
          v-model="commandInput"
          :placeholder="$t('command.placeholder')"
          @keyup.enter="executeCommand"
        >
          <template #append>
            <el-button 
              type="primary" 
              :loading="isExecuting"
              @click="executeCommand"
            >
              {{ isExecuting ? $t('command.executing') : $t('command.execute') }}
            </el-button>
          </template>
        </el-input>
      </div>
    </el-card>

    <el-card class="command-logs-card">
      <div class="logs-header">
        <h3>{{ $t('command.logs') }}</h3>
        <el-button 
          size="small" 
          @click="clearLogs"
          :disabled="!feedbackStore.commandLogs"
        >
          {{ $t('command.clear') }}
        </el-button>
      </div>
      
      <div class="logs-content">
        <pre v-if="feedbackStore.commandLogs" class="logs-text">{{ feedbackStore.commandLogs }}</pre>
        <el-empty v-else :description="$t('command.noLogs')" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useFeedbackStore } from '@/stores/feedback'

const feedbackStore = useFeedbackStore()
const commandInput = ref('')
const isExecuting = ref(false)

async function executeCommand() {
  if (!commandInput.value.trim() || isExecuting.value) return
  
  isExecuting.value = true
  const command = commandInput.value.trim()
  
  try {
    // 添加命令到日誌
    feedbackStore.appendCommandLog(`$ ${command}`)
    
    // 模擬命令執行
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模擬命令輸出
    const mockOutput = `Command executed: ${command}\nOutput: Success\nTimestamp: ${new Date().toLocaleString()}`
    feedbackStore.appendCommandLog(mockOutput)
    
    commandInput.value = ''
    ElMessage.success('命令執行完成')
  } catch (error) {
    feedbackStore.appendCommandLog(`Error: ${error.message}`)
    ElMessage.error('命令執行失敗')
  } finally {
    isExecuting.value = false
  }
}

function clearLogs() {
  feedbackStore.clearCommandLogs()
  ElMessage.info('日誌已清除')
}
</script>

<style scoped>
.command-view {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.command-input-card {
  margin-bottom: 20px;
}

.command-input-area {
  margin-bottom: 0;
}

.command-logs-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color);
}

.logs-content {
  flex: 1;
  overflow: hidden;
}

.logs-text {
  background: var(--el-fill-color-darker);
  color: var(--el-text-color-primary);
  padding: 16px;
  border-radius: 8px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  height: 100%;
  overflow-y: auto;
  margin: 0;
}
</style>
