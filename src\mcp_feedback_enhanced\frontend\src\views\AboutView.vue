<template>
  <div class="about-view">
    <div class="about-container">
      <div class="about-header">
        <div class="app-icon">
          <el-icon size="64"><ChatDotRound /></el-icon>
        </div>
        <h1>{{ $t('app.title') }}</h1>
        <p class="app-description">{{ $t('app.description') }}</p>
      </div>

      <el-card class="about-card">
        <el-descriptions :column="1" border>
          <el-descriptions-item :label="$t('about.version')">
            <el-tag type="primary">v3.0.0</el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('about.author')">
            Minidoracat
          </el-descriptions-item>
          <el-descriptions-item :label="$t('about.license')">
            MIT License
          </el-descriptions-item>
          <el-descriptions-item :label="$t('about.repository')">
            <el-link 
              href="https://github.com/Minidoracat/mcp-feedback-enhanced" 
              target="_blank"
              type="primary"
            >
              GitHub Repository
            </el-link>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card class="tech-stack-card">
        <h3>技術棧</h3>
        <div class="tech-grid">
          <div class="tech-item">
            <el-tag effect="dark">Vue 3</el-tag>
          </div>
          <div class="tech-item">
            <el-tag effect="dark" type="success">Electron</el-tag>
          </div>
          <div class="tech-item">
            <el-tag effect="dark" type="warning">Element Plus</el-tag>
          </div>
          <div class="tech-item">
            <el-tag effect="dark" type="info">Pinia</el-tag>
          </div>
          <div class="tech-item">
            <el-tag effect="dark" type="danger">Vite</el-tag>
          </div>
          <div class="tech-item">
            <el-tag effect="dark">FastAPI</el-tag>
          </div>
        </div>
      </el-card>

      <div class="environment-info" v-if="environmentInfo">
        <el-card>
          <h3>運行環境</h3>
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="平台">
              {{ environmentInfo.platform }}
            </el-descriptions-item>
            <el-descriptions-item label="Electron">
              {{ environmentInfo.isElectron ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item label="Node.js" v-if="environmentInfo.versions?.node">
              {{ environmentInfo.versions.node }}
            </el-descriptions-item>
            <el-descriptions-item label="Chrome" v-if="environmentInfo.versions?.chrome">
              {{ environmentInfo.versions.chrome }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ChatDotRound } from '@element-plus/icons-vue'

const environmentInfo = ref(null)

onMounted(() => {
  // 獲取環境信息
  if (window.environment) {
    environmentInfo.value = window.environment
  }
})
</script>

<style scoped>
.about-view {
  padding: 20px;
  height: 100%;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-overlay) 100%);
}

.about-container {
  max-width: 600px;
  margin: 0 auto;
}

.about-header {
  text-align: center;
  margin-bottom: 40px;
}

.app-icon {
  margin-bottom: 20px;
  color: var(--el-color-primary);
}

.about-header h1 {
  margin-bottom: 10px;
  color: var(--el-text-color-primary);
  font-size: 2.5em;
  font-weight: bold;
}

.app-description {
  color: var(--el-text-color-regular);
  font-size: 1.2em;
  margin-bottom: 0;
}

.about-card {
  margin-bottom: 20px;
}

.tech-stack-card {
  margin-bottom: 20px;
}

.tech-stack-card h3 {
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.tech-item {
  text-align: center;
}

.environment-info {
  margin-top: 20px;
}

.environment-info h3 {
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
}
</style>
