<template>
  <div class="settings-view">
    <h2>{{ $t('settings.title') }}</h2>
    
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 語言設定 -->
      <el-tab-pane :label="$t('settings.language')" name="language">
        <div class="setting-section">
          <el-form label-width="120px">
            <el-form-item :label="$t('settings.language')">
              <el-select v-model="settingsStore.language" @change="handleLanguageChange">
                <el-option
                  v-for="locale in supportedLocales"
                  :key="locale.code"
                  :label="`${locale.flag} ${locale.name}`"
                  :value="locale.code"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 主題設定 -->
      <el-tab-pane :label="$t('settings.theme')" name="theme">
        <div class="setting-section">
          <el-form label-width="120px">
            <el-form-item label="主題色彩">
              <el-color-picker v-model="settingsStore.themeColor" @change="settingsStore.setThemeColor" />
            </el-form-item>
            <el-form-item label="字體大小">
              <el-radio-group v-model="settingsStore.fontSize" @change="settingsStore.setFontSize">
                <el-radio label="small">小</el-radio>
                <el-radio label="medium">中</el-radio>
                <el-radio label="large">大</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 佈局設定 -->
      <el-tab-pane :label="$t('settings.layout')" name="layout">
        <div class="setting-section">
          <el-form label-width="120px">
            <el-form-item label="佈局模式">
              <el-radio-group v-model="settingsStore.layoutMode" @change="settingsStore.setLayoutMode">
                <el-radio label="separate">{{ $t('layout.separate') }}</el-radio>
                <el-radio label="combined-vertical">{{ $t('layout.combinedVertical') }}</el-radio>
                <el-radio label="combined-horizontal">{{ $t('layout.combinedHorizontal') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 進階設定 -->
      <el-tab-pane :label="$t('settings.advanced')" name="advanced">
        <div class="setting-section">
          <el-form label-width="120px">
            <el-form-item label="自動連接">
              <el-switch v-model="settingsStore.autoConnect" />
            </el-form-item>
            <el-form-item label="啟用通知">
              <el-switch v-model="settingsStore.enableNotifications" />
            </el-form-item>
            <el-form-item label="啟用音效">
              <el-switch v-model="settingsStore.enableSounds" />
            </el-form-item>
            <el-form-item label="自動儲存">
              <el-switch v-model="settingsStore.autoSave" />
            </el-form-item>
            <el-form-item label="除錯模式">
              <el-switch v-model="settingsStore.debugMode" />
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>

    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings">
        {{ $t('settings.save') }}
      </el-button>
      <el-button @click="resetSettings">
        {{ $t('settings.reset') }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { useSettingsStore } from '@/stores/settings'
import { supportedLocales, setLocale } from '@/i18n'

const { locale } = useI18n()
const settingsStore = useSettingsStore()
const activeTab = ref('language')

function handleLanguageChange(newLocale) {
  setLocale({ global: { locale } }, newLocale)
  settingsStore.setLanguage(newLocale)
  ElMessage.success('語言設定已更新')
}

function saveSettings() {
  settingsStore.saveSettings()
  ElMessage.success(settingsStore.language === 'zh-TW' ? '設定已儲存' : 'Settings saved')
}

function resetSettings() {
  settingsStore.resetToDefaults()
  ElMessage.info(settingsStore.language === 'zh-TW' ? '設定已重置' : 'Settings reset')
}
</script>

<style scoped>
.settings-view {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  height: 100%;
}

.setting-section {
  padding: 20px 0;
}

.settings-actions {
  margin-top: 30px;
  text-align: center;
}

.settings-actions .el-button {
  margin: 0 10px;
}
</style>
