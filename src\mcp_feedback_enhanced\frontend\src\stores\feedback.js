import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useApiStore } from './api'

export const useFeedbackStore = defineStore('feedback', () => {
  // 狀態
  const feedbackText = ref('')
  const images = ref([])
  const commandLogs = ref('')
  const isSubmitting = ref(false)
  const sessionId = ref(null)
  const projectDirectory = ref('')
  const aiSummary = ref('')
  
  // 超時控制
  const timeoutEnabled = ref(false)
  const timeoutDuration = ref(600) // 預設10分鐘
  const timeoutRemaining = ref(0)
  const timeoutTimer = ref(null)
  
  // 計算屬性
  const hasContent = computed(() => {
    return feedbackText.value.trim() || 
           images.value.length > 0 || 
           commandLogs.value.trim()
  })
  
  const canSubmit = computed(() => {
    return hasContent.value && !isSubmitting.value
  })
  
  const timeoutProgress = computed(() => {
    if (!timeoutEnabled.value || timeoutDuration.value === 0) return 0
    return ((timeoutDuration.value - timeoutRemaining.value) / timeoutDuration.value) * 100
  })
  
  // 動作
  function setFeedbackText(text) {
    feedbackText.value = text
  }
  
  function addImage(imageData) {
    if (images.value.length < 10) { // 限制最多10張圖片
      images.value.push({
        id: Date.now() + Math.random(),
        data: imageData.data,
        name: imageData.name || `image_${images.value.length + 1}`,
        size: imageData.size || 0,
        type: imageData.type || 'image/png'
      })
    }
  }
  
  function removeImage(imageId) {
    const index = images.value.findIndex(img => img.id === imageId)
    if (index > -1) {
      images.value.splice(index, 1)
    }
  }
  
  function clearImages() {
    images.value = []
  }
  
  function setCommandLogs(logs) {
    commandLogs.value = logs
  }
  
  function appendCommandLog(log) {
    commandLogs.value += log + '\n'
  }
  
  function clearCommandLogs() {
    commandLogs.value = ''
  }
  
  function setSessionInfo(info) {
    sessionId.value = info.sessionId
    projectDirectory.value = info.projectDirectory || ''
    aiSummary.value = info.summary || ''
  }
  
  function startTimeout(duration = timeoutDuration.value) {
    if (timeoutTimer.value) {
      clearInterval(timeoutTimer.value)
    }
    
    timeoutEnabled.value = true
    timeoutDuration.value = duration
    timeoutRemaining.value = duration
    
    timeoutTimer.value = setInterval(() => {
      timeoutRemaining.value--
      
      if (timeoutRemaining.value <= 0) {
        stopTimeout()
        // 觸發超時事件
        handleTimeout()
      }
    }, 1000)
  }
  
  function stopTimeout() {
    if (timeoutTimer.value) {
      clearInterval(timeoutTimer.value)
      timeoutTimer.value = null
    }
    timeoutEnabled.value = false
    timeoutRemaining.value = 0
  }
  
  function handleTimeout() {
    // 超時處理邏輯
    console.log('Session timeout occurred')
    // 可以在這裡添加自動提交或其他邏輯
  }
  
  async function submitFeedback() {
    if (!canSubmit.value) return null

    isSubmitting.value = true

    try {
      const apiStore = useApiStore()

      // 提交回饋到 API
      const result = await apiStore.submitFeedback(
        feedbackText.value,
        images.value,
        {
          commandLogs: commandLogs.value,
          timestamp: new Date().toISOString()
        }
      )

      // 停止超時計時器
      stopTimeout()

      console.log('Feedback submitted successfully:', result)
      return result
    } catch (error) {
      console.error('Submit feedback error:', error)
      throw error
    } finally {
      isSubmitting.value = false
    }
  }
  
  function reset() {
    feedbackText.value = ''
    images.value = []
    commandLogs.value = ''
    stopTimeout()
  }
  
  function resetSession() {
    reset()
    sessionId.value = null
    projectDirectory.value = ''
    aiSummary.value = ''
  }

  async function executeCommand(command) {
    try {
      const apiStore = useApiStore()
      const result = await apiStore.executeCommand(command)

      // 添加命令結果到日誌
      const logEntry = `$ ${command}\n${result.output}`
      appendCommandLog(logEntry)

      return result
    } catch (error) {
      console.error('Execute command error:', error)
      const errorLog = `$ ${command}\nError: ${error.message}`
      appendCommandLog(errorLog)
      throw error
    }
  }

  async function initializeSession() {
    try {
      const apiStore = useApiStore()
      apiStore.initializeFromEnv()

      if (apiStore.sessionId) {
        const sessionInfo = await apiStore.getSession()
        setSessionInfo({
          sessionId: sessionInfo.session_id,
          projectDirectory: sessionInfo.project_directory,
          summary: sessionInfo.summary
        })

        // 啟動超時計時器
        if (sessionInfo.timeout > 0) {
          const elapsed = Math.floor(Date.now() / 1000 - sessionInfo.created_at)
          const remaining = Math.max(0, sessionInfo.timeout - elapsed)
          if (remaining > 0) {
            startTimeout(remaining)
          }
        }

        console.log('Session initialized:', sessionInfo)
        return true
      }

      return false
    } catch (error) {
      console.error('Initialize session error:', error)
      return false
    }
  }
  
  return {
    // 狀態
    feedbackText,
    images,
    commandLogs,
    isSubmitting,
    sessionId,
    projectDirectory,
    aiSummary,
    timeoutEnabled,
    timeoutDuration,
    timeoutRemaining,
    
    // 計算屬性
    hasContent,
    canSubmit,
    timeoutProgress,
    
    // 動作
    setFeedbackText,
    addImage,
    removeImage,
    clearImages,
    setCommandLogs,
    appendCommandLog,
    clearCommandLogs,
    setSessionInfo,
    startTimeout,
    stopTimeout,
    submitFeedback,
    executeCommand,
    initializeSession,
    reset,
    resetSession
  }
})
