{"app": {"title": "MCP <PERSON>ced", "description": "AI 輔助開發互動式回饋收集工具"}, "navigation": {"feedback": "回饋", "summary": "摘要", "command": "命令", "settings": "設定", "about": "關於"}, "feedback": {"title": "回饋收集", "placeholder": "請輸入您的回饋內容...", "imageUpload": "圖片上傳", "dragDropHint": "拖拽圖片到此處或點擊上傳", "pasteHint": "您也可以使用 Ctrl+V 貼上剪貼板中的圖片", "maxImages": "最多可上傳 {count} 張圖片", "imageSize": "圖片大小限制：{size}KB", "removeImage": "移除圖片", "clearAll": "清除全部", "submit": "提交回饋", "submitting": "提交中...", "success": "回饋提交成功", "error": "回饋提交失敗"}, "summary": {"title": "AI 工作摘要", "projectDir": "專案目錄", "content": "摘要內容", "empty": "暫無摘要內容"}, "command": {"title": "命令執行", "input": "輸入命令", "placeholder": "請輸入要執行的命令...", "execute": "執行", "executing": "執行中...", "clear": "清除日誌", "logs": "執行日誌", "noLogs": "暫無執行日誌", "timeout": "命令執行超時", "error": "命令執行錯誤"}, "settings": {"title": "設定", "language": "語言設定", "theme": "主題設定", "layout": "佈局設定", "server": "伺服器設定", "shortcuts": "快捷鍵設定", "advanced": "進階設定", "reset": "重置為預設值", "save": "儲存設定", "saved": "設定已儲存"}, "layout": {"separate": "分離模式", "combinedVertical": "合併模式（垂直）", "combinedHorizontal": "合併模式（水平）"}, "timeout": {"enabled": "啟用超時", "duration": "超時時間", "remaining": "剩餘時間", "expired": "已超時", "warning": "即將超時"}, "websocket": {"connecting": "連接中...", "connected": "已連接", "disconnected": "已斷線", "error": "連接錯誤", "reconnecting": "重新連接中..."}, "about": {"title": "關於", "version": "版本", "author": "作者", "description": "描述", "license": "授權", "repository": "程式碼庫", "documentation": "說明文件", "support": "技術支援"}, "common": {"ok": "確定", "cancel": "取消", "yes": "是", "no": "否", "save": "儲存", "delete": "刪除", "edit": "編輯", "close": "關閉", "loading": "載入中...", "error": "錯誤", "success": "成功", "warning": "警告", "info": "資訊"}, "errors": {"networkError": "網路連接錯誤", "serverError": "伺服器錯誤", "invalidInput": "輸入無效", "fileTooBig": "檔案過大", "unsupportedFormat": "不支援的格式", "uploadFailed": "上傳失敗", "commandFailed": "命令執行失敗", "timeoutError": "操作超時", "unknownError": "未知錯誤"}, "shortcuts": {"submit": "提交", "clear": "清除", "newSession": "新建會話", "toggleLayout": "切換佈局", "focusInput": "聚焦輸入框"}}