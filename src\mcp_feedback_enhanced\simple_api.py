#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的 HTTP API 服務器
=====================

替代複雜的 WebSocket 實現，提供簡單的 REST API 與 Electron 前端通信。
專注於核心功能：會話管理、回饋收集、圖片處理。
"""

import asyncio
import json
import os
import subprocess
import threading
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

from .debug import debug_log


class FeedbackRequest(BaseModel):
    """回饋請求模型"""
    feedback_text: str = ""
    images: List[Dict[str, Any]] = []
    settings: Dict[str, Any] = {}


class CommandRequest(BaseModel):
    """命令執行請求模型"""
    command: str


class SessionInfo(BaseModel):
    """會話信息模型"""
    project_directory: str
    summary: str
    timeout: int = 600


class SimpleFeedbackSession:
    """簡化的回饋會話"""
    
    def __init__(self, session_id: str, project_directory: str, summary: str, timeout: int = 600):
        self.session_id = session_id
        self.project_directory = project_directory
        self.summary = summary
        self.timeout = timeout
        self.created_at = time.time()
        self.feedback_text = ""
        self.images = []
        self.command_logs = []
        self.is_completed = False
        self.result = None
        
    def add_feedback(self, feedback_text: str, images: List[Dict] = None):
        """添加回饋"""
        self.feedback_text = feedback_text
        if images:
            self.images.extend(images)
        debug_log(f"會話 {self.session_id} 添加回饋: {len(feedback_text)} 字符, {len(images or [])} 張圖片")
    
    def add_command_log(self, command: str, output: str, return_code: int = 0):
        """添加命令日誌"""
        log_entry = {
            "command": command,
            "output": output,
            "return_code": return_code,
            "timestamp": time.time()
        }
        self.command_logs.append(log_entry)
        debug_log(f"會話 {self.session_id} 添加命令日誌: {command}")
    
    def complete(self):
        """完成會話"""
        self.is_completed = True
        self.result = {
            "interactive_feedback": self.feedback_text,
            "images": self.images,
            "command_logs": "\n".join([
                f"$ {log['command']}\n{log['output']}" 
                for log in self.command_logs
            ])
        }
        debug_log(f"會話 {self.session_id} 已完成")
        return self.result
    
    def is_expired(self) -> bool:
        """檢查會話是否過期"""
        return time.time() - self.created_at > self.timeout


class SimpleAPIServer:
    """簡化的 API 服務器"""
    
    def __init__(self, host: str = "127.0.0.1", port: int = 8765):
        self.host = host
        self.port = port
        self.app = FastAPI(title="MCP Feedback Enhanced - Simple API")
        self.sessions: Dict[str, SimpleFeedbackSession] = {}
        self.server_thread = None
        self.electron_process = None
        
        # 設置 CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 設置路由
        self._setup_routes()
        
        debug_log(f"SimpleAPIServer 初始化完成，將在 {self.host}:{self.port} 啟動")
    
    def _setup_routes(self):
        """設置 API 路由"""
        
        @self.app.post("/api/sessions")
        async def create_session(session_info: SessionInfo):
            """創建新會話"""
            session_id = str(uuid.uuid4())
            session = SimpleFeedbackSession(
                session_id=session_id,
                project_directory=session_info.project_directory,
                summary=session_info.summary,
                timeout=session_info.timeout
            )
            self.sessions[session_id] = session
            debug_log(f"創建新會話: {session_id}")
            return {"session_id": session_id}
        
        @self.app.get("/api/sessions/{session_id}")
        async def get_session(session_id: str):
            """獲取會話信息"""
            session = self.sessions.get(session_id)
            if not session:
                raise HTTPException(status_code=404, detail="會話不存在")
            
            return {
                "session_id": session.session_id,
                "project_directory": session.project_directory,
                "summary": session.summary,
                "timeout": session.timeout,
                "created_at": session.created_at,
                "is_completed": session.is_completed,
                "feedback_text": session.feedback_text,
                "images_count": len(session.images),
                "command_logs_count": len(session.command_logs)
            }
        
        @self.app.post("/api/sessions/{session_id}/feedback")
        async def submit_feedback(session_id: str, feedback: FeedbackRequest):
            """提交回饋"""
            session = self.sessions.get(session_id)
            if not session:
                raise HTTPException(status_code=404, detail="會話不存在")
            
            session.add_feedback(feedback.feedback_text, feedback.images)
            result = session.complete()
            
            return {"success": True, "result": result}
        
        @self.app.post("/api/sessions/{session_id}/commands")
        async def execute_command(session_id: str, command_req: CommandRequest):
            """執行命令"""
            session = self.sessions.get(session_id)
            if not session:
                raise HTTPException(status_code=404, detail="會話不存在")
            
            try:
                # 在會話的專案目錄中執行命令
                result = subprocess.run(
                    command_req.command,
                    shell=True,
                    cwd=session.project_directory,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                output = result.stdout + result.stderr
                session.add_command_log(command_req.command, output, result.returncode)
                
                return {
                    "success": True,
                    "output": output,
                    "return_code": result.returncode
                }
                
            except subprocess.TimeoutExpired:
                error_msg = "命令執行超時（30秒）"
                session.add_command_log(command_req.command, error_msg, -1)
                return {
                    "success": False,
                    "output": error_msg,
                    "return_code": -1
                }
            except Exception as e:
                error_msg = f"命令執行錯誤: {str(e)}"
                session.add_command_log(command_req.command, error_msg, -1)
                return {
                    "success": False,
                    "output": error_msg,
                    "return_code": -1
                }
        
        @self.app.get("/api/sessions/{session_id}/result")
        async def get_result(session_id: str):
            """獲取會話結果"""
            session = self.sessions.get(session_id)
            if not session:
                raise HTTPException(status_code=404, detail="會話不存在")
            
            if not session.is_completed:
                return {"completed": False}
            
            return {"completed": True, "result": session.result}
        
        @self.app.delete("/api/sessions/{session_id}")
        async def delete_session(session_id: str):
            """刪除會話"""
            if session_id in self.sessions:
                del self.sessions[session_id]
                debug_log(f"刪除會話: {session_id}")
            return {"success": True}
        
        @self.app.get("/api/health")
        async def health_check():
            """健康檢查"""
            return {"status": "ok", "sessions": len(self.sessions)}
    
    def start_server(self):
        """啟動服務器"""
        if self.server_thread and self.server_thread.is_alive():
            debug_log("服務器已在運行")
            return
        
        self.server_thread = threading.Thread(
            target=self._run_server,
            daemon=True
        )
        self.server_thread.start()
        debug_log(f"API 服務器啟動線程已創建")
    
    def _run_server(self):
        """運行服務器"""
        try:
            uvicorn.run(
                self.app,
                host=self.host,
                port=self.port,
                log_level="warning"
            )
        except Exception as e:
            debug_log(f"API 服務器運行錯誤: {e}")
    
    def stop_server(self):
        """停止服務器"""
        # 清理會話
        self.sessions.clear()
        debug_log("API 服務器已停止")


# 全局服務器實例
_api_server = None


def get_api_server() -> SimpleAPIServer:
    """獲取 API 服務器實例"""
    global _api_server
    if _api_server is None:
        _api_server = SimpleAPIServer()
    return _api_server


def start_simple_api_server(host: str = "127.0.0.1", port: int = 8765) -> SimpleAPIServer:
    """啟動簡化的 API 服務器"""
    server = get_api_server()
    server.host = host
    server.port = port
    server.start_server()
    return server


def stop_simple_api_server():
    """停止簡化的 API 服務器"""
    global _api_server
    if _api_server:
        _api_server.stop_server()
        _api_server = None
