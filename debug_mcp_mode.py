#!/usr/bin/env python3
"""
調試 MCP 模式選擇的腳本
"""

import os
import sys
sys.path.insert(0, 'src')

from mcp_feedback_enhanced.environment_manager import get_environment_manager
from mcp_feedback_enhanced.server import is_remote_environment, can_use_gui

def debug_environment():
    """調試環境檢測"""
    print("=== 環境檢測調試 ===")
    
    # 基本環境檢測
    is_remote = is_remote_environment()
    gui_available = can_use_gui()
    
    print(f"遠端環境: {is_remote}")
    print(f"GUI 可用: {gui_available}")
    
    # 環境變數檢查
    print("\n=== 環境變數 ===")
    env_vars = [
        "SSH_CONNECTION", "SSH_CLIENT", "DISPLAY", 
        "VSCODE_INJECTION", "SESSIONNAME", "FORCE_WEB"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        print(f"{var}: {value or '未設置'}")
    
    # 環境管理器檢測
    print("\n=== 環境管理器 ===")
    manager = get_environment_manager()
    print(f"遠端環境: {manager.is_remote}")
    print(f"GUI 可用: {manager.gui_available}")
    print(f"Electron 可用: {manager.electron_available}")
    print(f"推薦模式: {manager.get_recommended_mode()}")
    
    # Electron 詳細檢測
    print("\n=== Electron 檢測 ===")
    launcher = manager.electron_launcher
    print(f"前端路徑: {launcher.frontend_path}")
    print(f"Electron 可用: {launcher.is_electron_available()}")
    
    if launcher.frontend_path:
        package_json = launcher.frontend_path / "package.json"
        node_modules = launcher.frontend_path / "node_modules"
        print(f"package.json 存在: {package_json.exists()}")
        print(f"node_modules 存在: {node_modules.exists()}")

if __name__ == "__main__":
    debug_environment()
