import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useApiStore = defineStore('api', () => {
  // 狀態
  const baseUrl = ref('http://127.0.0.1:8765/api')
  const sessionId = ref(null)
  const isConnected = ref(false)
  const lastError = ref(null)
  
  // 計算屬性
  const apiUrl = computed(() => baseUrl.value)
  
  // 動作
  async function createSession(projectDirectory, summary, timeout = 600) {
    try {
      const response = await fetch(`${baseUrl.value}/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_directory: projectDirectory,
          summary: summary,
          timeout: timeout
        })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      sessionId.value = data.session_id
      isConnected.value = true
      lastError.value = null
      
      console.log('Session created:', sessionId.value)
      return data.session_id
    } catch (error) {
      console.error('Failed to create session:', error)
      lastError.value = error
      isConnected.value = false
      throw error
    }
  }
  
  async function getSession(id = sessionId.value) {
    if (!id) {
      throw new Error('No session ID provided')
    }
    
    try {
      const response = await fetch(`${baseUrl.value}/sessions/${id}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      return data
    } catch (error) {
      console.error('Failed to get session:', error)
      lastError.value = error
      throw error
    }
  }
  
  async function submitFeedback(feedbackText, images = [], settings = {}) {
    if (!sessionId.value) {
      throw new Error('No active session')
    }
    
    try {
      const response = await fetch(`${baseUrl.value}/sessions/${sessionId.value}/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          feedback_text: feedbackText,
          images: images,
          settings: settings
        })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      console.log('Feedback submitted successfully')
      return data
    } catch (error) {
      console.error('Failed to submit feedback:', error)
      lastError.value = error
      throw error
    }
  }
  
  async function executeCommand(command) {
    if (!sessionId.value) {
      throw new Error('No active session')
    }
    
    try {
      const response = await fetch(`${baseUrl.value}/sessions/${sessionId.value}/commands`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command: command
        })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      console.log('Command executed:', command)
      return data
    } catch (error) {
      console.error('Failed to execute command:', error)
      lastError.value = error
      throw error
    }
  }
  
  async function getResult(id = sessionId.value) {
    if (!id) {
      throw new Error('No session ID provided')
    }
    
    try {
      const response = await fetch(`${baseUrl.value}/sessions/${id}/result`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      return data
    } catch (error) {
      console.error('Failed to get result:', error)
      lastError.value = error
      throw error
    }
  }
  
  async function deleteSession(id = sessionId.value) {
    if (!id) {
      return
    }
    
    try {
      const response = await fetch(`${baseUrl.value}/sessions/${id}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        console.log('Session deleted:', id)
      }
    } catch (error) {
      console.error('Failed to delete session:', error)
    } finally {
      if (id === sessionId.value) {
        sessionId.value = null
        isConnected.value = false
      }
    }
  }
  
  async function healthCheck() {
    try {
      const response = await fetch(`${baseUrl.value}/health`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      return data
    } catch (error) {
      console.error('Health check failed:', error)
      lastError.value = error
      throw error
    }
  }
  
  function disconnect() {
    if (sessionId.value) {
      deleteSession()
    }
    sessionId.value = null
    isConnected.value = false
    lastError.value = null
  }
  
  // 初始化時檢查環境變數
  function initializeFromEnv() {
    // 檢查是否有環境變數設置的會話 ID
    const envSessionId = window.environment?.sessionId || 
                        process.env.MCP_SESSION_ID ||
                        new URLSearchParams(window.location.search).get('session_id')
    
    if (envSessionId) {
      sessionId.value = envSessionId
      isConnected.value = true
      console.log('Initialized with session ID from environment:', envSessionId)
    }
    
    // 檢查是否有自定義 API URL
    const envApiUrl = window.environment?.apiUrl || 
                     process.env.MCP_API_URL
    
    if (envApiUrl) {
      baseUrl.value = envApiUrl
      console.log('Using API URL from environment:', envApiUrl)
    }
  }
  
  return {
    // 狀態
    baseUrl,
    sessionId,
    isConnected,
    lastError,
    
    // 計算屬性
    apiUrl,
    
    // 動作
    createSession,
    getSession,
    submitFeedback,
    executeCommand,
    getResult,
    deleteSession,
    healthCheck,
    disconnect,
    initializeFromEnv
  }
})
