#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Electron 啟動器
==============

負責檢測、啟動和管理 Electron 前端應用。
提供與簡化 API 服務器的整合。
"""

import os
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any

from .debug import debug_log


class ElectronLauncher:
    """Electron 啟動器（簡化版）"""

    def __init__(self):
        self.frontend_path = self._find_frontend_path()
        
    def _find_frontend_path(self) -> Optional[Path]:
        """查找前端目錄"""
        # 從當前文件位置向上查找 frontend 目錄
        current_path = Path(__file__).parent
        
        # 嘗試多個可能的路徑
        possible_paths = [
            current_path / "frontend",  # 新位置：src/mcp_feedback_enhanced/frontend
            current_path.parent.parent.parent / "frontend",  # 舊位置（向後兼容）
            Path.cwd() / "frontend",  # 當前工作目錄
        ]
        
        for path in possible_paths:
            if path.exists() and (path / "package.json").exists():
                debug_log(f"找到前端目錄: {path}")
                return path
        
        debug_log("未找到前端目錄")
        return None
    
    def is_electron_available(self) -> bool:
        """檢查 Electron 是否可用"""
        if not self.frontend_path:
            return False
        
        try:
            # 檢查 package.json 是否存在
            package_json = self.frontend_path / "package.json"
            if not package_json.exists():
                return False
            
            # 檢查 node_modules 是否存在
            node_modules = self.frontend_path / "node_modules"
            if not node_modules.exists():
                debug_log("node_modules 不存在，嘗試安裝依賴")
                return self._install_dependencies()
            
            return True
            
        except Exception as e:
            debug_log(f"檢查 Electron 可用性時出錯: {e}")
            return False
    
    def _install_dependencies(self) -> bool:
        """安裝前端依賴"""
        try:
            debug_log("開始安裝前端依賴...")
            result = subprocess.run(
                ["npm", "install"],
                cwd=self.frontend_path,
                capture_output=True,
                text=True,
                timeout=300  # 5分鐘超時
            )
            
            if result.returncode == 0:
                debug_log("前端依賴安裝成功")
                return True
            else:
                debug_log(f"前端依賴安裝失敗: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            debug_log("前端依賴安裝超時")
            return False
        except Exception as e:
            debug_log(f"安裝前端依賴時出錯: {e}")
            return False
    
    # 移除複雜的啟動邏輯，改由 environment_manager.py 處理
    
    # 移除複雜的方法，改由 environment_manager.py 處理


# 全局啟動器實例
_electron_launcher = None


def get_electron_launcher() -> ElectronLauncher:
    """獲取 Electron 啟動器實例"""
    global _electron_launcher
    if _electron_launcher is None:
        _electron_launcher = ElectronLauncher()
    return _electron_launcher


# 啟動邏輯已移至 environment_manager.py
