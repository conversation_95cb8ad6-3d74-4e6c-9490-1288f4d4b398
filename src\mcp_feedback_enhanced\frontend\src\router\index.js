import { createRouter, createWebHistory } from 'vue-router'

// 路由組件懶加載
const FeedbackView = () => import('@/views/FeedbackView.vue')
const SummaryView = () => import('@/views/SummaryView.vue')
const CommandView = () => import('@/views/CommandView.vue')
const SettingsView = () => import('@/views/SettingsView.vue')
const AboutView = () => import('@/views/AboutView.vue')

const routes = [
  {
    path: '/',
    name: 'feedback',
    component: FeedbackView,
    meta: {
      title: 'feedback.title',
      icon: 'ChatDotRound'
    }
  },
  {
    path: '/summary',
    name: 'summary',
    component: SummaryView,
    meta: {
      title: 'summary.title',
      icon: 'Document'
    }
  },
  {
    path: '/command',
    name: 'command',
    component: CommandView,
    meta: {
      title: 'command.title',
      icon: 'Monitor'
    }
  },
  {
    path: '/settings',
    name: 'settings',
    component: SettingsView,
    meta: {
      title: 'settings.title',
      icon: 'Setting'
    }
  },
  {
    path: '/about',
    name: 'about',
    component: AboutView,
    meta: {
      title: 'about.title',
      icon: 'InfoFilled'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守衛
router.beforeEach((to, from, next) => {
  // 設置頁面標題
  if (to.meta.title) {
    document.title = `MCP Feedback Enhanced - ${to.meta.title}`
  }
  
  next()
})

export default router
