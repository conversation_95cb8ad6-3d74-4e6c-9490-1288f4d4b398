{"app": {"title": "MCP <PERSON>ced", "description": "Interactive feedback collection tool for AI-assisted development"}, "navigation": {"feedback": "<PERSON><PERSON><PERSON>", "summary": "Summary", "command": "Command", "settings": "Settings", "about": "About"}, "feedback": {"title": "Feedback Collection", "placeholder": "Please enter your feedback...", "imageUpload": "Image Upload", "dragDropHint": "Drag images here or click to upload", "pasteHint": "You can also use Ctrl+V to paste images from clipboard", "maxImages": "Maximum {count} images allowed", "imageSize": "Image size limit: {size}KB", "removeImage": "Remove image", "clearAll": "Clear all", "submit": "Submit <PERSON>", "submitting": "Submitting...", "success": "<PERSON><PERSON><PERSON> submitted successfully", "error": "Failed to submit feedback"}, "summary": {"title": "AI Work Summary", "projectDir": "Project Directory", "content": "Summary Content", "empty": "No summary content"}, "command": {"title": "Command Execution", "input": "Enter command", "placeholder": "Please enter command to execute...", "execute": "Execute", "executing": "Executing...", "clear": "Clear logs", "logs": "Execution logs", "noLogs": "No execution logs", "timeout": "Command execution timeout", "error": "Command execution error"}, "settings": {"title": "Settings", "language": "Language Settings", "theme": "Theme Settings", "layout": "Layout Settings", "server": "Server Settings", "shortcuts": "Shortcut Settings", "advanced": "Advanced Settings", "reset": "Reset to defaults", "save": "Save Settings", "saved": "Setting<PERSON> saved"}, "layout": {"separate": "Separate Mode", "combinedVertical": "Combined Mode (Vertical)", "combinedHorizontal": "Combined Mode (Horizontal)"}, "timeout": {"enabled": "Enable timeout", "duration": "Timeout duration", "remaining": "Time remaining", "expired": "Timeout expired", "warning": "Timeout warning"}, "websocket": {"connecting": "Connecting...", "connected": "Connected", "disconnected": "Disconnected", "error": "Connection error", "reconnecting": "Reconnecting..."}, "about": {"title": "About", "version": "Version", "author": "Author", "description": "Description", "license": "License", "repository": "Repository", "documentation": "Documentation", "support": "Support"}, "common": {"ok": "OK", "cancel": "Cancel", "yes": "Yes", "no": "No", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info"}, "errors": {"networkError": "Network connection error", "serverError": "Server error", "invalidInput": "Invalid input", "fileTooBig": "File too big", "unsupportedFormat": "Unsupported format", "uploadFailed": "Upload failed", "commandFailed": "Command execution failed", "timeoutError": "Operation timeout", "unknownError": "Unknown error"}, "shortcuts": {"submit": "Submit", "clear": "Clear", "newSession": "New session", "toggleLayout": "Toggle layout", "focusInput": "Focus input"}}