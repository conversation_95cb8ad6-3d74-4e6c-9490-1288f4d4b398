#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
統一界面模組
============

整合 Electron + Vue 前端與現有 MCP 系統的橋接模組。
提供統一的界面啟動和管理功能。
"""

import os
import sys
import subprocess
import threading
import time
import socket
from pathlib import Path
from typing import Optional, Dict, Any

from ..debug import debug_log
from ..web import WebUIManager, launch_web_feedback_ui


class UnifiedUIManager:
    """統一界面管理器"""
    
    def __init__(self):
        self.frontend_path = Path(__file__).parent.parent.parent.parent / "frontend"
        self.electron_process = None
        self.web_manager = None
        self.is_electron_available = self._check_electron_availability()
        
    def _check_electron_availability(self) -> bool:
        """檢查 Electron 前端是否可用"""
        try:
            # 檢查 frontend 目錄是否存在
            if not self.frontend_path.exists():
                debug_log("Frontend 目錄不存在")
                return False
                
            # 檢查 package.json 是否存在
            package_json = self.frontend_path / "package.json"
            if not package_json.exists():
                debug_log("package.json 不存在")
                return False
                
            # 檢查 node_modules 是否存在（依賴是否已安裝）
            node_modules = self.frontend_path / "node_modules"
            if not node_modules.exists():
                debug_log("Node.js 依賴未安裝，請運行: cd frontend && npm install")
                return False
                
            debug_log("Electron 前端可用")
            return True
            
        except Exception as e:
            debug_log(f"檢查 Electron 可用性時出錯: {e}")
            return False
    
    def _find_free_port(self, start_port: int = 8765) -> int:
        """尋找可用端口"""
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue
        return start_port
    
    async def launch_unified_feedback_ui(
        self, 
        project_directory: str, 
        summary: str, 
        timeout: int = 600,
        force_web: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        啟動統一回饋界面
        
        Args:
            project_directory: 專案目錄
            summary: AI 工作摘要
            timeout: 超時時間
            force_web: 強制使用 Web UI
            
        Returns:
            回饋結果字典
        """
        
        # 決定使用哪種界面
        use_electron = (
            not force_web and 
            self.is_electron_available and 
            not self._is_remote_environment()
        )
        
        if use_electron:
            debug_log("啟動 Electron 統一界面")
            return await self._launch_electron_ui(project_directory, summary, timeout)
        else:
            debug_log("啟動 Web 統一界面")
            return await self._launch_web_ui(project_directory, summary, timeout)
    
    async def _launch_electron_ui(
        self, 
        project_directory: str, 
        summary: str, 
        timeout: int
    ) -> Optional[Dict[str, Any]]:
        """啟動 Electron 界面"""
        try:
            # 首先啟動後端 Web 服務器
            port = self._find_free_port()
            self.web_manager = WebUIManager(port=port)
            
            # 在後台啟動 Web 服務器
            web_thread = threading.Thread(
                target=self._start_web_server,
                args=(port,),
                daemon=True
            )
            web_thread.start()
            
            # 等待服務器啟動（優化：減少等待時間）
            time.sleep(0.5)
            
            # 啟動 Electron 前端
            electron_cmd = [
                "npm", "run", "electron:dev"
            ]
            
            # 設置環境變數
            env = os.environ.copy()
            env.update({
                "MCP_PROJECT_DIR": project_directory,
                "MCP_SUMMARY": summary,
                "MCP_TIMEOUT": str(timeout),
                "MCP_BACKEND_PORT": str(port),
                "NODE_ENV": "development"
            })
            
            debug_log(f"啟動 Electron: {' '.join(electron_cmd)}")
            
            self.electron_process = subprocess.Popen(
                electron_cmd,
                cwd=str(self.frontend_path),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待 Electron 進程結束或超時
            try:
                stdout, stderr = self.electron_process.communicate(timeout=timeout)
                
                if self.electron_process.returncode == 0:
                    debug_log("Electron 界面正常關閉")
                    # 從 Web 服務器獲取結果
                    return await self._get_feedback_result()
                else:
                    debug_log(f"Electron 界面異常退出: {stderr.decode()}")
                    return None
                    
            except subprocess.TimeoutExpired:
                debug_log("Electron 界面超時")
                self.electron_process.kill()
                return None
                
        except Exception as e:
            debug_log(f"啟動 Electron 界面失敗: {e}")
            # 回退到 Web 界面
            return await self._launch_web_ui(project_directory, summary, timeout)
        finally:
            self._cleanup()
    
    async def _launch_web_ui(
        self, 
        project_directory: str, 
        summary: str, 
        timeout: int
    ) -> Optional[Dict[str, Any]]:
        """啟動 Web 界面（回退方案）"""
        return await launch_web_feedback_ui(project_directory, summary, timeout)
    
    def _start_web_server(self, port: int):
        """啟動 Web 服務器"""
        try:
            import uvicorn
            uvicorn.run(
                self.web_manager.app,
                host="127.0.0.1",
                port=port,
                log_level="warning"
            )
        except Exception as e:
            debug_log(f"Web 服務器啟動失敗: {e}")
    
    async def _get_feedback_result(self) -> Optional[Dict[str, Any]]:
        """從 Web 服務器獲取回饋結果"""
        # 這裡需要實現從 Web 服務器獲取結果的邏輯
        # 可以通過 WebSocket 或 HTTP API 獲取
        return {
            "interactive_feedback": "",
            "images": [],
            "logs": ""
        }
    
    def _is_remote_environment(self) -> bool:
        """檢測是否為遠端環境"""
        return (
            os.getenv('SSH_CLIENT') is not None or
            os.getenv('SSH_TTY') is not None or
            os.getenv('REMOTE_CONTAINERS') is not None or
            os.getenv('CODESPACES') is not None
        )
    
    def _cleanup(self):
        """清理資源"""
        if self.electron_process:
            try:
                self.electron_process.terminate()
                self.electron_process.wait(timeout=5)
            except:
                self.electron_process.kill()
            self.electron_process = None
            
        if self.web_manager:
            # 停止 Web 服務器
            self.web_manager = None


# 全局實例
_unified_manager = None

def get_unified_manager() -> UnifiedUIManager:
    """獲取統一界面管理器實例"""
    global _unified_manager
    if _unified_manager is None:
        _unified_manager = UnifiedUIManager()
    return _unified_manager


async def launch_unified_feedback_ui(
    project_directory: str, 
    summary: str, 
    timeout: int = 600
) -> Optional[Dict[str, Any]]:
    """
    啟動統一回饋界面的便捷函數
    
    這個函數會自動選擇最適合的界面：
    1. 如果 Electron 前端可用且在本地環境，使用 Electron + Vue 界面
    2. 否則回退到現有的 Web UI
    """
    manager = get_unified_manager()
    
    # 檢查是否強制使用 Web UI
    force_web = os.getenv('FORCE_WEB', '').lower() in ('true', '1', 'yes')
    
    return await manager.launch_unified_feedback_ui(
        project_directory, 
        summary, 
        timeout, 
        force_web
    )
