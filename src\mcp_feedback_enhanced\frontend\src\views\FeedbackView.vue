<template>
  <div class="feedback-view">
    <div class="feedback-container">
      <h2>{{ $t('feedback.title') }}</h2>

      <!-- 專案信息 -->
      <el-card class="project-info" v-if="feedbackStore.projectDirectory">
        <div class="project-header">
          <el-icon><Folder /></el-icon>
          <span>{{ $t('summary.projectDir') }}: {{ feedbackStore.projectDirectory }}</span>
        </div>
      </el-card>

      <!-- 回饋輸入區域 -->
      <el-card class="feedback-input">
        <el-input
          v-model="feedbackStore.feedbackText"
          type="textarea"
          :placeholder="$t('feedback.placeholder')"
          :rows="8"
          resize="vertical"
          maxlength="5000"
          show-word-limit
        />
      </el-card>

      <!-- 圖片上傳區域 -->
      <el-card class="image-upload">
        <div class="upload-header">
          <h3>{{ $t('feedback.imageUpload') }}</h3>
          <el-button
            v-if="feedbackStore.images.length > 0"
            type="danger"
            size="small"
            @click="feedbackStore.clearImages()"
          >
            {{ $t('feedback.clearAll') }}
          </el-button>
        </div>

        <el-upload
          class="upload-area"
          drag
          multiple
          :auto-upload="false"
          :on-change="handleImageUpload"
          :show-file-list="false"
          accept="image/*"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            {{ $t('feedback.dragDropHint') }}
          </div>
          <div class="el-upload__tip">
            {{ $t('feedback.pasteHint') }}
          </div>
        </el-upload>

        <!-- 圖片預覽 -->
        <div v-if="feedbackStore.images.length > 0" class="image-preview">
          <div
            v-for="image in feedbackStore.images"
            :key="image.id"
            class="image-item"
          >
            <img :src="image.data" :alt="image.name" />
            <div class="image-overlay">
              <span class="image-name">{{ image.name }}</span>
              <el-button
                type="danger"
                size="small"
                circle
                @click="feedbackStore.removeImage(image.id)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按鈕 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          size="large"
          :loading="feedbackStore.isSubmitting"
          :disabled="!feedbackStore.canSubmit"
          @click="handleSubmit"
        >
          {{ feedbackStore.isSubmitting ? $t('feedback.submitting') : $t('feedback.submit') }}
        </el-button>

        <el-button
          size="large"
          @click="handleClear"
        >
          {{ $t('feedback.clearAll') }}
        </el-button>
      </div>

      <!-- 超時顯示 -->
      <div v-if="feedbackStore.timeoutEnabled" class="timeout-display">
        <el-progress
          :percentage="feedbackStore.timeoutProgress"
          :color="getTimeoutColor()"
          :show-text="false"
        />
        <div class="timeout-text">
          {{ $t('timeout.remaining') }}: {{ formatTime(feedbackStore.timeoutRemaining) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Folder, UploadFilled, Delete
} from '@element-plus/icons-vue'
import { useFeedbackStore } from '@/stores/feedback'

const feedbackStore = useFeedbackStore()

onMounted(() => {
  // 設置測試數據
  feedbackStore.setSessionInfo({
    sessionId: 'test-session-' + Date.now(),
    projectDirectory: 'G:\\github\\mcp-feedback-enhanced',
    summary: '這是一個測試摘要，展示 AI 工作完成的內容。'
  })
  
  // 監聽剪貼板貼上事件
  document.addEventListener('paste', handlePaste)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})

function handleImageUpload(file) {
  const reader = new FileReader()
  reader.onload = (e) => {
    feedbackStore.addImage({
      data: e.target.result,
      name: file.name,
      size: file.size,
      type: file.raw.type
    })
  }
  reader.readAsDataURL(file.raw)
}

function handlePaste(event) {
  const items = event.clipboardData?.items
  if (!items) return
  
  for (let item of items) {
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      const reader = new FileReader()
      reader.onload = (e) => {
        feedbackStore.addImage({
          data: e.target.result,
          name: `clipboard_${Date.now()}.png`,
          size: file.size,
          type: file.type
        })
      }
      reader.readAsDataURL(file)
    }
  }
}

async function handleSubmit() {
  try {
    await feedbackStore.submitFeedback()
    ElMessage.success('回饋提交成功！')
  } catch (error) {
    ElMessage.error('回饋提交失敗：' + error.message)
  }
}

function handleClear() {
  feedbackStore.reset()
  ElMessage.info('已清除所有內容')
}

function getTimeoutColor() {
  const progress = feedbackStore.timeoutProgress
  if (progress > 80) return '#f56c6c'
  if (progress > 60) return '#e6a23c'
  return '#409eff'
}

function formatTime(seconds) {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}
</script>

<style scoped>
.feedback-view {
  padding: 20px;
  height: 100%;
}

.feedback-container {
  max-width: 800px;
  margin: 0 auto;
}

.feedback-container h2 {
  margin-bottom: 20px;
  color: var(--el-text-color-primary);
}

.project-info {
  margin-bottom: 20px;
}

.project-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-regular);
}

.feedback-input {
  margin-bottom: 20px;
}

.image-upload {
  margin-bottom: 20px;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.upload-area {
  margin-bottom: 16px;
}

.image-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: var(--el-fill-color-light);
}

.image-item img {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.image-name {
  color: white;
  font-size: 12px;
  word-break: break-all;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
}

.timeout-display {
  background: var(--el-fill-color-light);
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.timeout-text {
  margin-top: 8px;
  color: var(--el-text-color-regular);
  font-size: 14px;
}
</style>
