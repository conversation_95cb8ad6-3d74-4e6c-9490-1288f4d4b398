#!/usr/bin/env python3
"""
模擬 MCP 調用的測試腳本
"""

import asyncio
import sys
import os
sys.path.insert(0, 'src')

from mcp_feedback_enhanced.server import interactive_feedback

async def test_mcp_call():
    """測試 MCP 調用"""
    print("=== 模擬 MCP 調用測試 ===")

    # 先檢查環境
    from mcp_feedback_enhanced.environment_manager import get_environment_manager
    manager = get_environment_manager()
    print(f"環境檢測結果:")
    print(f"  - 遠端環境: {manager.is_remote}")
    print(f"  - GUI 可用: {manager.gui_available}")
    print(f"  - Electron 可用: {manager.electron_available}")
    print(f"  - 推薦模式: {manager.get_recommended_mode()}")

    try:
        print("\n開始 MCP 調用...")
        # 模擬 MCP 調用
        result = await interactive_feedback(
            project_directory=".",
            summary="測試 MCP 調用是否正確啟動 Electron",
            timeout=30  # 短超時用於測試
        )

        print(f"MCP 調用結果: {result}")

    except Exception as e:
        print(f"MCP 調用失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 設置調試模式
    os.environ["MCP_DEBUG"] = "true"
    
    asyncio.run(test_mcp_call())
