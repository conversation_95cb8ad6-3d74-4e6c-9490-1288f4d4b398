{"name": "mcp-feedback-enhanced-frontend", "version": "3.0.0", "description": "Unified frontend for MCP Feedback Enhanced using Electron + Vue", "main": "electron/main.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on --timeout 10000 http://localhost:5173 && electron .\"", "electron:build": "npm run build && electron-builder", "electron:pack": "npm run build && electron-builder --dir", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vue-i18n": "^9.8.0", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "electron": "^28.1.0", "electron-builder": "^24.9.1", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}, "build": {"appId": "com.minidoracat.mcp-feedback-enhanced", "productName": "MCP <PERSON>ced", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "keywords": ["mcp", "feedback", "electron", "vue", "ai-assistant"], "author": "Minidoracat <<EMAIL>>", "license": "MIT"}