#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
環境管理器
==========

統一管理本地和遠端環境的檢測與啟動邏輯。
"""

import os
import sys
import asyncio
from typing import Optional, Dict, Any, Tu<PERSON>
from pathlib import Path

from .debug import server_debug_log as debug_log
from .server import is_remote_environment, can_use_gui
from .electron_launcher import ElectronLauncher


class EnvironmentManager:
    """環境管理器"""
    
    def __init__(self):
        self.is_remote = is_remote_environment()
        self.gui_available = can_use_gui()
        self.electron_launcher = ElectronLauncher()
        self.electron_available = self.electron_launcher.is_electron_available()

        # 進程引用
        self._vite_process = None
        self._electron_process = None

        debug_log(f"環境管理器初始化:")
        debug_log(f"  - 遠端環境: {self.is_remote}")
        debug_log(f"  - GUI 可用: {self.gui_available}")
        debug_log(f"  - Electron 可用: {self.electron_available}")
        debug_log(f"  - 推薦模式: {self.get_recommended_mode()}")
    
    def get_recommended_mode(self) -> str:
        """獲取推薦的運行模式"""
        # 檢查是否強制使用 Web UI
        force_web = os.getenv('FORCE_WEB', '').lower() in ('true', '1', 'yes', 'on')
        if force_web:
            debug_log("檢測到 FORCE_WEB 環境變數，強制使用 Web UI")
            return "web"

        # MCP 調用時優先使用 Electron（除非明確禁用）
        if self.electron_available:
            return "electron"   # 優先使用 Electron
        elif self.is_remote:
            return "websocket"  # 遠端環境使用 WebSocket 服務器
        else:
            return "web"        # 回退到 Web UI
    
    def get_environment_info(self) -> Dict[str, Any]:
        """獲取環境信息"""
        return {
            "is_remote": self.is_remote,
            "gui_available": self.gui_available,
            "electron_available": self.electron_available,
            "recommended_mode": self.get_recommended_mode(),
            "platform": sys.platform,
            "python_version": sys.version.split()[0],
            "environment_variables": {
                "SSH_CONNECTION": os.getenv("SSH_CONNECTION"),
                "SSH_CLIENT": os.getenv("SSH_CLIENT"),
                "DISPLAY": os.getenv("DISPLAY"),
                "VSCODE_INJECTION": os.getenv("VSCODE_INJECTION"),
                "SESSIONNAME": os.getenv("SESSIONNAME"),
                "FORCE_WEB": os.getenv("FORCE_WEB"),
            }
        }
    
    async def launch_feedback_interface(
        self, 
        project_directory: str = ".", 
        summary: str = "我已完成了您請求的任務。",
        timeout: int = 600
    ) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        啟動回饋介面
        
        Args:
            project_directory: 專案目錄路徑
            summary: AI 工作完成的摘要說明
            timeout: 等待用戶回饋的超時時間（秒）
            
        Returns:
            Tuple[bool, Optional[Dict]]: (成功標誌, 結果數據)
        """
        mode = self.get_recommended_mode()
        debug_log(f"啟動回饋介面，模式: {mode}")
        
        try:
            if mode == "websocket":
                return await self._launch_websocket_mode(project_directory, summary, timeout)
            elif mode == "electron":
                return await self._launch_electron_mode(project_directory, summary, timeout)
            else:  # web
                return await self._launch_web_mode(project_directory, summary, timeout)
                
        except Exception as e:
            debug_log(f"啟動回饋介面失敗: {e}")
            return False, None
    
    async def _launch_websocket_mode(
        self, 
        project_directory: str, 
        summary: str, 
        timeout: int
    ) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """啟動 WebSocket 服務器模式（遠端環境）"""
        debug_log("啟動 WebSocket 服務器模式")
        
        try:
            from .web.main import WebUIManager
            from .web.models.feedback_session import WebFeedbackSession as FeedbackSession
            
            # 創建 Web UI 管理器
            manager = WebUIManager()

            # 創建會話
            session_id = manager.create_session(project_directory, summary)
            session = manager.get_session(session_id)
            
            # 啟動服務器
            manager.start_server()
            
            debug_log(f"WebSocket 服務器已啟動，會話 ID: {session.session_id}")
            debug_log(f"等待遠端 Electron 連接到: ws://localhost:8000/ws/{session.session_id}")
            
            # 等待會話完成
            result = await session.wait_for_feedback(timeout)
            
            return True, result
            
        except Exception as e:
            debug_log(f"WebSocket 模式啟動失敗: {e}")
            return False, None
    
    async def _launch_electron_mode(
        self,
        project_directory: str,
        summary: str,
        timeout: int
    ) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """啟動 Electron 模式（本地環境）"""
        debug_log("啟動 Electron 模式")

        try:
            # 1. 啟動簡化的 WebSocket 服務器（本地模式）
            from .web.main import WebUIManager
            from .web.models.feedback_session import WebFeedbackSession as FeedbackSession

            # 創建 Web UI 管理器
            manager = WebUIManager()

            # 創建會話
            session_id = manager.create_session(project_directory, summary)
            session = manager.get_session(session_id)

            # 啟動服務器（非阻塞）
            manager.start_server()

            debug_log(f"本地 WebSocket 服務器已啟動，會話 ID: {session.session_id}")
            debug_log(f"服務器地址: ws://{manager.host}:{manager.port}/ws/{session.session_id}")

            # 2. 啟動 Electron 應用
            success = await self._launch_electron_app(
                session.session_id,
                manager.host,
                manager.port
            )

            if not success:
                debug_log("❌ Electron 應用啟動失敗，回退到 Web 模式")
                debug_log("🔍 檢查 Electron 啟動失敗的原因：")
                debug_log(f"   - Electron 可用: {self.electron_launcher.is_electron_available()}")
                debug_log(f"   - 前端路徑: {self.electron_launcher.frontend_path}")
                debug_log(f"   - npm 命令: {self._find_npm_command()}")
                return await self._launch_web_mode(project_directory, summary, timeout)

            debug_log("Electron 應用已啟動，等待用戶回饋...")

            # 3. 等待會話完成
            result = await session.wait_for_feedback(timeout)

            return True, result

        except Exception as e:
            debug_log(f"Electron 模式啟動失敗: {e}")
            return False, None

    async def _launch_electron_app(
        self,
        session_id: str,
        server_host: str,
        server_port: int
    ) -> bool:
        """啟動 Electron 應用"""
        try:
            import subprocess
            import asyncio

            # 檢查 Electron 是否可用
            if not self.electron_launcher.is_electron_available():
                debug_log("Electron 不可用")
                return False

            # 構建 Electron 啟動命令
            frontend_path = self.electron_launcher.frontend_path
            electron_main = frontend_path / "electron" / "main.js"

            if not electron_main.exists():
                debug_log(f"Electron 主文件不存在: {electron_main}")
                return False

            # 檢查 npm 是否可用
            npm_cmd = self._find_npm_command()
            if not npm_cmd:
                debug_log("找不到 npm 命令")
                return False

            # 首先啟動 Vite 開發服務器
            debug_log("啟動 Vite 開發服務器...")
            vite_process = subprocess.Popen(
                [npm_cmd, "run", "dev"],
                cwd=str(frontend_path),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                shell=True  # 在 Windows 上使用 shell
            )

            # 等待 Vite 服務器啟動（優化：減少等待時間）
            await asyncio.sleep(1)

            # 啟動 Electron 命令
            cmd = [
                npm_cmd, "run", "electron",
                "--",
                "--session-id", session_id,
                "--server-host", server_host,
                "--server-port", str(server_port),
                "--mode", "local"
            ]

            debug_log(f"啟動 Electron 命令: {' '.join(cmd)}")

            # 啟動 Electron 進程（非阻塞）
            electron_process = subprocess.Popen(
                cmd,
                cwd=str(frontend_path),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                shell=True  # 在 Windows 上使用 shell
            )

            debug_log(f"Vite 進程 PID: {vite_process.pid}")
            debug_log(f"Electron 進程 PID: {electron_process.pid}")
            debug_log("Electron 應用已啟動，視窗應該已顯示")

            # 儲存進程引用以便後續清理
            self._vite_process = vite_process
            self._electron_process = electron_process

            return True

        except Exception as e:
            debug_log(f"啟動 Electron 應用失敗: {e}")
            return False

    def _find_npm_command(self) -> Optional[str]:
        """尋找 npm 命令"""
        import shutil

        # 嘗試不同的 npm 命令
        npm_commands = ["npm", "npm.cmd", "npm.exe"]

        for cmd in npm_commands:
            if shutil.which(cmd):
                debug_log(f"找到 npm 命令: {cmd}")
                return cmd

        debug_log("未找到 npm 命令")
        return None

    def cleanup_processes(self):
        """清理 Electron 和 Vite 進程"""
        if self._electron_process:
            try:
                self._electron_process.terminate()
                debug_log("Electron 進程已終止")
            except Exception as e:
                debug_log(f"終止 Electron 進程失敗: {e}")
            finally:
                self._electron_process = None

        if self._vite_process:
            try:
                self._vite_process.terminate()
                debug_log("Vite 進程已終止")
            except Exception as e:
                debug_log(f"終止 Vite 進程失敗: {e}")
            finally:
                self._vite_process = None
    
    async def _launch_web_mode(
        self,
        project_directory: str,
        summary: str,
        timeout: int
    ) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """啟動 Web 模式（回退選項）"""
        debug_log("啟動 Web 模式")

        try:
            # 使用傳統 Web UI 啟動邏輯
            from .web.main import launch_web_feedback_ui

            result = await launch_web_feedback_ui(project_directory, summary, timeout)
            return True, result

        except Exception as e:
            debug_log(f"Web 模式啟動失敗: {e}")
            return False, None


# 全局環境管理器實例
_environment_manager: Optional[EnvironmentManager] = None


def get_environment_manager() -> EnvironmentManager:
    """獲取環境管理器實例（單例模式）"""
    global _environment_manager
    if _environment_manager is None:
        _environment_manager = EnvironmentManager()
    return _environment_manager


async def launch_smart_feedback(
    project_directory: str = ".", 
    summary: str = "我已完成了您請求的任務。",
    timeout: int = 600
) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    智能啟動回饋介面（統一入口）
    
    Args:
        project_directory: 專案目錄路徑
        summary: AI 工作完成的摘要說明
        timeout: 等待用戶回饋的超時時間（秒）
        
    Returns:
        Tuple[bool, Optional[Dict]]: (成功標誌, 結果數據)
    """
    manager = get_environment_manager()
    return await manager.launch_feedback_interface(project_directory, summary, timeout)
