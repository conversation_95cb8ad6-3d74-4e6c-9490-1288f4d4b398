import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export const useSettingsStore = defineStore('settings', () => {
  // 狀態
  const language = ref('zh-TW')
  const themeColor = ref('#409eff')
  const layoutMode = ref('separate') // 'separate', 'combined-vertical', 'combined-horizontal'
  const autoConnect = ref(true)
  const enableNotifications = ref(true)
  const enableSounds = ref(false)
  const fontSize = ref('medium') // 'small', 'medium', 'large'
  const autoSave = ref(true)
  const autoSaveInterval = ref(30) // 秒
  const maxImageSize = ref(1024) // KB
  const maxImages = ref(10)
  const commandTimeout = ref(30) // 秒
  const debugMode = ref(false)
  const sidebarCollapsed = ref(false) // 側邊欄收縮狀態
  
  // 服務器設定
  const serverHost = ref('127.0.0.1')
  const serverPort = ref(8000)
  const useHttps = ref(false)
  const apiTimeout = ref(10000) // 毫秒
  
  // 快捷鍵設定
  const shortcuts = ref({
    submit: 'Ctrl+Enter',
    clear: 'Ctrl+Shift+C',
    newSession: 'Ctrl+N',
    toggleLayout: 'Ctrl+L',
    focusInput: 'Ctrl+I'
  })
  
  // 計算屬性
  const serverUrl = computed(() => {
    const protocol = useHttps.value ? 'https' : 'http'
    return `${protocol}://${serverHost.value}:${serverPort.value}`
  })
  
  const wsUrl = computed(() => {
    const protocol = useHttps.value ? 'wss' : 'ws'
    const sessionId = sessionInfo.value?.sessionId || ''
    const baseUrl = `${protocol}://${serverHost.value}:${serverPort.value}/ws`
    return sessionId ? `${baseUrl}/${sessionId}` : baseUrl
  })

  // 會話信息
  const sessionInfo = ref(null)
  
  // 動作
  function setLanguage(lang) {
    language.value = lang
    saveSettings()
  }
  
  function setThemeColor(color) {
    themeColor.value = color
    applyThemeColor()
    saveSettings()
  }
  
  function setLayoutMode(mode) {
    layoutMode.value = mode
    saveSettings()
  }
  
  function setFontSize(size) {
    fontSize.value = size
    applyFontSize()
    saveSettings()
  }
  
  function updateShortcut(action, shortcut) {
    shortcuts.value[action] = shortcut
    saveSettings()
  }
  
  function setSessionInfo(info) {
    sessionInfo.value = info
    if (info) {
      // 更新服務器配置
      if (info.serverHost) serverHost.value = info.serverHost
      if (info.serverPort) serverPort.value = info.serverPort
      console.log('會話信息已更新:', info)
    }
  }

  function resetToDefaults() {
    language.value = 'zh-TW'
    themeColor.value = '#409eff'
    layoutMode.value = 'separate'
    autoConnect.value = true
    enableNotifications.value = true
    enableSounds.value = false
    fontSize.value = 'medium'
    autoSave.value = true
    autoSaveInterval.value = 30
    maxImageSize.value = 1024
    maxImages.value = 10
    commandTimeout.value = 30
    debugMode.value = false
    sidebarCollapsed.value = false

    serverHost.value = '127.0.0.1'
    serverPort.value = 8000
    useHttps.value = false
    apiTimeout.value = 10000

    shortcuts.value = {
      submit: 'Ctrl+Enter',
      clear: 'Ctrl+Shift+C',
      newSession: 'Ctrl+N',
      toggleLayout: 'Ctrl+L',
      focusInput: 'Ctrl+I'
    }

    saveSettings()
  }
  
  function applyThemeColor() {
    document.documentElement.style.setProperty('--el-color-primary', themeColor.value)
  }
  
  function applyFontSize() {
    const sizes = {
      small: '12px',
      medium: '14px',
      large: '16px'
    }
    document.documentElement.style.setProperty('--app-font-size', sizes[fontSize.value])
  }
  
  function saveSettings() {
    const settings = {
      language: language.value,
      themeColor: themeColor.value,
      layoutMode: layoutMode.value,
      autoConnect: autoConnect.value,
      enableNotifications: enableNotifications.value,
      enableSounds: enableSounds.value,
      fontSize: fontSize.value,
      autoSave: autoSave.value,
      autoSaveInterval: autoSaveInterval.value,
      maxImageSize: maxImageSize.value,
      maxImages: maxImages.value,
      commandTimeout: commandTimeout.value,
      debugMode: debugMode.value,
      sidebarCollapsed: sidebarCollapsed.value,
      serverHost: serverHost.value,
      serverPort: serverPort.value,
      useHttps: useHttps.value,
      apiTimeout: apiTimeout.value,
      shortcuts: shortcuts.value
    }
    
    try {
      localStorage.setItem('mcp-feedback-settings', JSON.stringify(settings))
      if (debugMode.value) {
        console.log('Settings saved:', settings)
      }
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }
  
  async function loadSettings() {
    try {
      const saved = localStorage.getItem('mcp-feedback-settings')
      if (saved) {
        const settings = JSON.parse(saved)
        
        // 載入設定
        language.value = settings.language || 'zh-TW'
        themeColor.value = settings.themeColor || '#409eff'
        layoutMode.value = settings.layoutMode || 'separate'
        autoConnect.value = settings.autoConnect ?? true
        enableNotifications.value = settings.enableNotifications ?? true
        enableSounds.value = settings.enableSounds ?? false
        fontSize.value = settings.fontSize || 'medium'
        autoSave.value = settings.autoSave ?? true
        autoSaveInterval.value = settings.autoSaveInterval || 30
        maxImageSize.value = settings.maxImageSize || 1024
        maxImages.value = settings.maxImages || 10
        commandTimeout.value = settings.commandTimeout || 30
        debugMode.value = settings.debugMode ?? false
        sidebarCollapsed.value = settings.sidebarCollapsed ?? false
        
        serverHost.value = settings.serverHost || '127.0.0.1'
        serverPort.value = settings.serverPort || 8000
        useHttps.value = settings.useHttps ?? false
        apiTimeout.value = settings.apiTimeout || 10000
        
        if (settings.shortcuts) {
          shortcuts.value = { ...shortcuts.value, ...settings.shortcuts }
        }
        
        // 應用設定
        applyThemeColor()
        applyFontSize()
        
        if (debugMode.value) {
          console.log('Settings loaded:', settings)
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }
  
  // 監聽設定變化並自動保存
  watch([
    language, themeColor, layoutMode, autoConnect, enableNotifications,
    enableSounds, fontSize, autoSave, autoSaveInterval, maxImageSize,
    maxImages, commandTimeout, debugMode, sidebarCollapsed, serverHost,
    serverPort, useHttps, apiTimeout
  ], () => {
    if (autoSave.value) {
      saveSettings()
    }
  }, { deep: true })
  
  return {
    // 狀態
    language,
    themeColor,
    layoutMode,
    autoConnect,
    enableNotifications,
    enableSounds,
    fontSize,
    autoSave,
    autoSaveInterval,
    maxImageSize,
    maxImages,
    commandTimeout,
    debugMode,
    sidebarCollapsed,
    serverHost,
    serverPort,
    useHttps,
    apiTimeout,
    shortcuts,
    sessionInfo,

    // 計算屬性
    serverUrl,
    wsUrl,

    // 動作
    setLanguage,
    setThemeColor,
    setLayoutMode,
    setFontSize,
    updateShortcut,
    setSessionInfo,
    resetToDefaults,
    saveSettings,
    loadSettings
  }
})
