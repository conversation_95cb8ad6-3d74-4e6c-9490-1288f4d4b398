#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP Interactive Feedback Enhanced - 主程式入口
==============================================

此檔案允許套件透過 `python -m mcp_feedback_enhanced` 執行。

使用方法:
  python -m mcp_feedback_enhanced        # 啟動 MCP 伺服器
  python -m mcp_feedback_enhanced test   # 執行測試
"""

import sys
import argparse
import os

def main():
    """主程式入口點"""
    parser = argparse.ArgumentParser(
        description="MCP Feedback Enhanced Enhanced - 互動式回饋收集 MCP 伺服器"
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 伺服器命令（預設）
    server_parser = subparsers.add_parser('server', help='啟動 MCP 伺服器（預設）')
    
    # 測試命令
    test_parser = subparsers.add_parser('test', help='執行測試（自動檢測環境）')
    
    # 版本命令
    version_parser = subparsers.add_parser('version', help='顯示版本資訊')
    
    args = parser.parse_args()
    
    if args.command == 'test':
        run_tests(args)
    elif args.command == 'version':
        show_version()
    elif args.command == 'server':
        run_server()
    elif args.command is None:
        run_server()
    else:
        # 不應該到達這裡
        parser.print_help()
        sys.exit(1)

def run_server():
    """啟動 MCP 伺服器"""
    from .server import main as server_main
    return server_main()

def run_tests(args):
    """執行測試（統一命令，自動環境檢測）"""
    # 啟用調試模式以顯示測試過程
    os.environ["MCP_DEBUG"] = "true"

    print("🧪 執行智能測試（自動環境檢測）...")

    # 導入環境檢測功能
    from .server import is_remote_environment, can_use_gui
    from .electron_launcher import ElectronLauncher

    # 環境檢測
    is_remote = is_remote_environment()
    gui_available = can_use_gui()

    print(f"🔍 環境檢測結果:")
    print(f"   - 遠端環境: {'是' if is_remote else '否'}")
    print(f"   - GUI 可用: {'是' if gui_available else '否'}")

    # 檢查 Electron 是否可用
    electron_launcher = ElectronLauncher()
    electron_available = electron_launcher.is_electron_available()
    print(f"   - Electron 可用: {'是' if electron_available else '否'}")

    success = True

    try:
        if is_remote:
            # 遠端環境：使用 WebSocket 模式
            print("🌐 遠端環境檢測到，啟動 WebSocket 服務器模式...")
            from .test_web_ui import test_web_ui, interactive_demo

            web_success, session_info = test_web_ui()
            if not web_success:
                success = False
            elif session_info:
                print("📝 WebSocket 服務器已啟動，等待遠端 Electron 連接...")
                print("💡 提示：請在本地運行 Electron 應用連接到此服務器")
                print("💡 按 Ctrl+C 停止服務器")
                interactive_demo(session_info)
        else:
            # 本地環境：優先使用 Electron
            if electron_available:
                print("🖥️  本地環境檢測到，啟動 Electron 應用...")

                # 使用環境管理器啟動 Electron
                from .environment_manager import get_environment_manager
                import asyncio

                async def run_electron():
                    manager = get_environment_manager()
                    success, result = await manager.launch_feedback_interface(
                        project_directory=".",
                        summary="測試 Electron 應用啟動",
                        timeout=600
                    )
                    return success, result

                try:
                    success, result = asyncio.run(run_electron())
                    if success:
                        print("✅ Electron 應用啟動成功！")
                    else:
                        print("❌ Electron 應用啟動失敗")
                except Exception as e:
                    print(f"❌ Electron 啟動過程中出錯: {e}")
                    success = False
            else:
                print("⚠️  Electron 不可用，回退到 Web UI 模式...")
                from .test_web_ui import test_web_ui
                web_success, _ = test_web_ui()
                success = web_success

    except Exception as e:
        print(f"❌ 測試執行失敗: {e}")
        success = False

    if not success:
        sys.exit(1)

    print("🎉 測試完成！")

def show_version():
    """顯示版本資訊"""
    from . import __version__, __author__
    print(f"MCP Feedback Enhanced Enhanced v{__version__}")
    print(f"作者: {__author__}")
    print("GitHub: https://github.com/Minidoracat/mcp-feedback-enhanced")

if __name__ == "__main__":
    main() 