import { contextBridge, ipcRenderer } from 'electron'

// 暴露安全的 API 給渲染進程
contextBridge.exposeInMainWorld('electronAPI', {
  // 應用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // 對話框
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  
  // 菜單事件監聽
  onMenuNewSession: (callback) => {
    ipcRenderer.on('menu-new-session', callback)
    return () => ipcRenderer.removeListener('menu-new-session', callback)
  },
  
  onMenuAbout: (callback) => {
    ipcRenderer.on('menu-about', callback)
    return () => ipcRenderer.removeListener('menu-about', callback)
  },
  
  // 系統信息
  platform: process.platform,
  
  // 文件操作
  openExternal: (url) => {
    // 通過主進程打開外部連結
    ipcRenderer.send('open-external', url)
  },

  // 啟動配置監聽
  onLaunchConfig: (callback) => {
    ipcRenderer.on('launch-config', (event, config) => callback(config))
    return () => ipcRenderer.removeListener('launch-config', callback)
  }
})

// 檢測運行環境
contextBridge.exposeInMainWorld('environment', {
  isElectron: true,
  platform: process.platform,
  versions: process.versions
})
